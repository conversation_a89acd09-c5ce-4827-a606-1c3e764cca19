
import { SubtitleLine } from '../types';

export const formatSecondsToSRTTime = (totalSeconds: number): string => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  const milliseconds = Math.floor((totalSeconds * 1000) % 1000);

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
};

const isValidSRTTimeFormat = (timeStr: string): boolean => {
  return /^\d{2}:\d{2}:\d{2},\d{3}$/.test(timeStr);
};

export const generateSRTContent = (subtitles: SubtitleLine[]): string => {
  let srtContent = '';
  subtitles.forEach((sub, index) => {
    if (!isValidSRTTimeFormat(sub.startTime) || !isValidSRTTimeFormat(sub.endTime)) {
      console.warn(`Invalid time format for subtitle ID ${sub.id}. Skipping.`);
      return; 
    }
    srtContent += `${index + 1}\n`;
    srtContent += `${sub.startTime} --> ${sub.endTime}\n`;
    srtContent += `${sub.text}\n\n`;
  });
  return srtContent;
};

export const downloadSRTFile = (srtContent: string, filename: string = 'subtitles.srt'): void => {
  const blob = new Blob([srtContent], { type: 'text/srt;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};
